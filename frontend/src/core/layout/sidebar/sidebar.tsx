import { SidebarMenuItemClient } from '@/core/layout/sidebar/sidebar-menu-item.client'
import { SidebarUserDetails } from '@/core/layout/sidebar/sidebar-user-details'
import { SidebarMenuGroup } from '@/core/layout/types'
import { getAppSessionAction } from '@/features/auth/actions'
import { SearchModal } from '@/features/search/components/organisms/search-modal'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/shared/components/molecules/collapsible'
import { isCreator } from '@/shared/utils'
import { CalendarDays, HomeIcon, NewspaperIcon, Utensils } from 'lucide-react'
import { NextIntlClientProvider } from 'next-intl'
import { getMessages, getTranslations } from 'next-intl/server'
import Image from 'next/image'
import { CreateDraftSidebarGroupAction } from './create-draft-sidebar-group-action'
import {
    SidebarContainer,
    SidebarContent,
    SidebarFooter,
    SidebarGroup,
    SidebarGroupContent,
    SidebarGroupLabel,
    SidebarHeader,
    SidebarMenu,
    SidebarMenuButton,
    SidebarMenuItem,
    SidebarMenuSub,
} from './sidebar-container'

export const Sidebar = async () => {
    const t = await getTranslations('Sidebar')
    const { sessionData } = await getAppSessionAction()

    const items: SidebarMenuGroup[] = [
        {
            label: 'Inhalte',
            action: isCreator(sessionData) ? <CreateDraftSidebarGroupAction /> : undefined,
            items: [
                { title: t('home'), url: '/', icon: HomeIcon },
                { title: t('news'), url: '/news', icon: NewspaperIcon },
                { title: t('events'), url: '/events', icon: CalendarDays },
                { title: t('canteen'), url: '/canteen', icon: Utensils },
            ],
        },
        {
            label: t('manage'),
            hidden: !isCreator(sessionData),
            items: [
                { title: 'Neuigkeiten', url: '/manage/news' },
                { title: 'Veranstaltungen', url: '/manage/events' },
                {
                    title: 'Speiseplan',
                    items: [
                        {title: 'Planung', url: '/manage/canteen/plan'},
                        {title: 'Menüs', url: '/manage/canteen/menus'},
                        {title: 'Gerichte', url: '/manage/canteen/dishes'},
                        {title: 'Hinweise', url: '/manage/canteen/notes'},
                    ],
                },
            ],
        },
    ]

    const messagesSearch = await getMessages()

    return (
        <SidebarContainer>
            <SidebarHeader>
                <div className="flex items-center justify-center">
                    <Image src="/logo.png" alt="logo" width={25} height={25} />
                    <h3 className="mx-3 text-lg font-semibold text-foreground">
                        Intranet
                    </h3>
                </div>
            </SidebarHeader>
            <SidebarContent>
                {items.map((group, index) => !group.hidden ? (
                    <SidebarGroup key={index}>
                        {group.label ? <SidebarGroupLabel>{group.label}</SidebarGroupLabel> : null}
                        {group.action}
                        <SidebarGroupContent>
                            <SidebarMenu>
                                {group.items.map((item, index) => {
                                    if (!item.hidden) {
                                        if ('items' in item) {
                                            return (
                                                <Collapsible key={index} defaultOpen className="group/collapsible">
                                                    <SidebarMenuItem>
                                                        <CollapsibleTrigger asChild>
                                                            <SidebarMenuButton>
                                                                {item.icon ? <item.icon /> : null}
                                                                <span>{item.title}</span>
                                                            </SidebarMenuButton>
                                                        </CollapsibleTrigger>
                                                        <CollapsibleContent>
                                                            <SidebarMenuSub>
                                                                {item.items.map((subItem, index) => (
                                                                    <SidebarMenuItemClient url={subItem.url} key={index}>
                                                                        <span>{subItem.title}</span>
                                                                    </SidebarMenuItemClient>
                                                                ))}
                                                            </SidebarMenuSub>
                                                        </CollapsibleContent>
                                                    </SidebarMenuItem>
                                                </Collapsible>
                                            )
                                        } else {
                                            return (
                                                <SidebarMenuItemClient url={item.url} key={index}>
                                                    {item.icon ? <item.icon /> : null}
                                                    <span>{item.title}</span>
                                                </SidebarMenuItemClient>
                                            )
                                        }
                                    }
                                })}
                            </SidebarMenu>
                        </SidebarGroupContent>
                    </SidebarGroup>
                ) : null)}
            </SidebarContent>
            <SidebarFooter>
                <NextIntlClientProvider messages={messagesSearch}>
                    <SearchModal permissions={sessionData?.permissions ?? []} />
                </NextIntlClientProvider>
                <SidebarUserDetails loggedInUser={sessionData?.user} />
            </SidebarFooter>
        </SidebarContainer>
    )
}
